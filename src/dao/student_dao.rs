use crate::client;
use crate::controller::demo_controller::Pagination;
use crate::entities::{prelude::*, *};
use crate::error::{AppError, AppResult};
use axum::extract::Query;
use chrono::{Local, NaiveDate, NaiveDateTime};
use rand::Rng;
use random_nickname2::{random, Gender};
use sea_orm::*;

#[derive(<PERSON>bu<PERSON>, <PERSON>lone)]
pub struct StudentRepository {
    db: DatabaseConnection,
}

impl StudentRepository {
    pub fn new(db: DatabaseConnection) -> Self {
        Self { db }
    }

    pub fn generate_age() -> i32 {
        let num = rand::thread_rng().gen_range(1..50);
        num
    }

    pub async fn insert(&self) -> AppResult<u64> {
        let global_id: i64 = client::global_id_grpc_client::gen_global_id()
            .await
            .unwrap();
        let nickname = random::<3>(Gender::Male).unwrap().to_string();
        let values = vec![
            global_id.into(),
            nickname.into(),
            Self::generate_age().into(),
            "12131231".into(),
            Local::now().naive_local().into(),
            Local::now().naive_local().into(),
        ];
        let result = self.db
            .execute(
                Statement::from_sql_and_values(
                    DatabaseBackend::MySql,
                    "INSERT INTO student (gid, nickname, age, id_card, create_time, update_time) VALUES (?, ?, ?, ?, ?, ?)",
                    values,
                )
            )
            .await?;
        Ok(result.rows_affected())
    }

    pub async fn update(&self, name: &str, id: i64) -> AppResult<u64> {
        let update_time = Local::now().naive_local();

        let res = self
            .db
            .execute(Statement::from_sql_and_values(
                DatabaseBackend::MySql,
                "UPDATE student SET nickname = ?, update_time = ? WHERE gid = ?",
                vec![name.into(), update_time.into(), id.into()],
            ))
            .await
            .map_err(|e| {
                //tracing::error!("Failed to update student: {}", e);
                AppError::Database(e)
            })?;
        Ok(res.rows_affected())
    }

    pub async fn find_by_id(&self, id: i64) -> AppResult<Option<student::Model>> {
        let student = Student::find_by_id(id).one(&self.db).await?;
        Ok(student)
    }

    pub async fn find_by_name(&self, name: &str) -> AppResult<Option<student::Model>> {
        let student = Student::find()
            .filter(student::Column::Nickname.eq(name))
            .one(&self.db)
            .await?;
        Ok(student)
    }

    pub async fn find_by_age(
        &self,
        pagination: Query<Pagination>,
    ) -> AppResult<Vec<student::Model>> {
        let (offset, limit) = pagination.calculate_offset_limit();
        let students = Student::find()
            .offset(Option::Some(offset))
            .limit(Some(limit))
            //.filter(student::Column::Age.lte(age))
            .all(&self.db)
            .await?;
        Ok(students)
    }

    pub async fn find_first(&self) -> AppResult<Option<student::Model>> {
        let student = Student::find()
            .order_by_desc(student::Column::Gid)
            .one(&self.db)
            .await?;
        Ok(student)
    }
}
