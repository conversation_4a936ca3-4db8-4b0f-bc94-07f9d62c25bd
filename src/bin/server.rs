use axum::routing::{get, post};
use axum::Router;
use rust_demo::config::config::DatabaseConfig;
use rust_demo::controller::demo_controller;
use rust_demo::controller::unique_id_server::GlobalIdService;
use rust_demo::dao::student_dao::StudentRepository;
use rust_demo::db;
use rust_demo::totoro_infrastructure::global_id_server::{GlobalId, GlobalIdServer};
use sea_orm::*;
use std::error::Error;
use std::net::SocketAddr;
use tonic::*;
use tower_http::cors::CorsLayer;
use rust_demo::model::app_status::AppState;

async fn run_servers() -> Result<(), Box<dyn std::error::Error>> {
    // 1. 创建服务
    let global_id_service = GlobalIdService::default();
    let grpc_service = GlobalIdServer::new(global_id_service);

    // 2. 设置服务器地址
    let http_addr = "0.0.0.0:3000".parse::<SocketAddr>()?;
    let grpc_addr = "0.0.0.0:9000".parse::<SocketAddr>()?;

    println!("HTTP 服务器运行在 {}", http_addr);
    println!("gRPC 服务器运行在 {}", grpc_addr);

    // 3. 创建 StudentRepository
    let config = DatabaseConfig::new();
    let conn = db::establish_connection(&config).await?;
    // 创建仓储实例
    // let student_repo = StudentRepository::new(conn);

    let state = AppState {
        student_repo: StudentRepository::new(conn),
        // teacher_repo: TeacherRepository::new(teacher_conn),
    };

    // 4. 启动两个服务器
    tokio::spawn(async move {
        // HTTP 服务器
        let app = Router::new()
            .route("/api/globalId", get(demo_controller::get_global_id))
            .route("/api/students/:gid", get(demo_controller::get_student_by_gid))
            .route("/api/students/new", post(demo_controller::new_student))
            .route("/api/students/update", post(demo_controller::update_student))
            .route("/api/students/query", get(demo_controller::get_student_by_age))
            .layer(CorsLayer::permissive())
            .with_state(state);

        println!("启动 HTTP 服务器...");
        axum::serve(
            tokio::net::TcpListener::bind(http_addr).await.unwrap(),
            app.into_make_service()
        )
            .await
            .unwrap();
    });

    // gRPC 服务器
    println!("启动 gRPC 服务器...");
    tonic::transport::Server::builder()
        .add_service(grpc_service)
        .serve(grpc_addr)
        .await?;

    Ok(())
}

// 修改 main 函数
#[tokio::main]
#[cfg(feature = "server")]
async fn main() -> Result<(), Box<dyn Error>> {
    run_servers().await
}



