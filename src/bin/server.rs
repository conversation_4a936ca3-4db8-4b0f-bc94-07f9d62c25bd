use unique_id_service::proto_gen::unique_id_service::UniqueIdServiceSyncProcessor;
use unique_id_service::service::UniqueIdService;
use std::env;
use thrift::protocol::{TBinaryInputProtocolFactory, TBinaryOutputProtocolFactory};
use thrift::server::TServer;
use thrift::transport::{
    TBufferedReadTransportFactory, TBufferedWriteTransportFactory,
};
use log::{info, error};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logger
    env_logger::init();

    info!("Starting Thrift UniqueId Service Server...");

    // Parse command line arguments
    let args: Vec<String> = env::args().collect();
    let worker_id = if args.len() > 1 {
        args[1].parse::<u64>().unwrap_or_else(|_| {
            eprintln!("Invalid worker_id: {}, using default", args[1]);
            std::process::id() as u64 % 1024
        })
    } else {
        std::process::id() as u64 % 1024
    };

    let listen_address = if args.len() > 2 {
        args[2].clone()
    } else {
        "127.0.0.1:9090".to_string()
    };

    // Create the service implementation
    let service = UniqueIdService::new(worker_id)
        .map_err(|e| format!("Failed to create service: {}", e))?;

    info!("Created UniqueIdService with worker_id: {}", worker_id);

    // Create the processor
    let processor = UniqueIdServiceSyncProcessor::new(service);

    // Create server
    info!("Server listening on {}", listen_address);

    let read_transport_factory = TBufferedReadTransportFactory::new();
    let write_transport_factory = TBufferedWriteTransportFactory::new();
    let input_protocol_factory = TBinaryInputProtocolFactory::new();
    let output_protocol_factory = TBinaryOutputProtocolFactory::new();

    let mut server = TServer::new(
        read_transport_factory,
        input_protocol_factory,
        write_transport_factory,
        output_protocol_factory,
        processor,
        1, // num_workers
    );

    info!("UniqueId Thrift Server started successfully!");
    info!("Usage: cargo run --bin server [worker_id] [listen_address]");
    info!("Example: cargo run --bin server 1 127.0.0.1:9090");

    match server.listen(&listen_address) {
        Ok(_) => {
            info!("Server stopped gracefully");
            Ok(())
        }
        Err(e) => {
            error!("Server error: {}", e);
            Err(Box::new(e))
        }
    }
}
