use axum::routing::{get, post};
use axum::Router;
use rust_demo::config::config::DatabaseConfig;
use rust_demo::controller::demo_controller;
use rust_demo::controller::unique_id_server::GlobalIdService;
use rust_demo::dao::student_dao::StudentRepository;
use rust_demo::db;
use rust_demo::model::app_status::AppState;
// 添加 Thrift 相关导入
use rust_demo::unique_id_service::thrift::UniqueIdServiceSyncProcessor;
use rust_demo::services::unique_id_thrift_service::UniqueIdThriftService;
use sea_orm::*;
use std::error::Error;
use std::net::SocketAddr;
use thrift::protocol::{TBinaryInputProtocolFactory, TBinaryOutputProtocolFactory};
use thrift::server::TServer;
use thrift::transport::{TBufferedReadTransportFactory, TBufferedWriteTransportFactory};
use tonic::*;
use tower_http::cors::CorsLayer;

async fn run_servers() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Starting Multi-Protocol Server (HTTP + gRPC + Thrift) ===");

    // 1. 创建 gRPC 服务
    let global_id_service = GlobalIdService::default();
    // let grpc_service = GlobalIdServer::new(global_id_service);

    // 2. 创建 Thrift 服务
    let thrift_service = UniqueIdThriftService::new(1)?;
    let thrift_processor = UniqueIdServiceSyncProcessor::new(thrift_service);

    // 3. 设置服务器地址
    let http_addr = "0.0.0.0:3000".parse::<SocketAddr>()?;
    let grpc_addr = "0.0.0.0:9000".parse::<SocketAddr>()?;
    let thrift_addr = "127.0.0.1:9090";

    println!("HTTP 服务器运行在 {}", http_addr);
    println!("gRPC 服务器运行在 {}", grpc_addr);
    println!("Thrift 服务器运行在 {}", thrift_addr);

    // 4. 创建 StudentRepository
    let config = DatabaseConfig::new();
    let conn = db::establish_connection(&config).await?;

    let state = AppState {
        student_repo: StudentRepository::new(conn),
    };

    // 5. 启动 HTTP 服务器
    tokio::spawn(async move {
        let app = Router::new()
            .route("/api/globalId", get(demo_controller::get_global_id))
            .route("/api/students/:gid", get(demo_controller::get_student_by_gid))
            .route("/api/students/new", post(demo_controller::new_student))
            .route("/api/students/update", post(demo_controller::update_student))
            .route("/api/students/query", get(demo_controller::get_student_by_age))
            .layer(CorsLayer::permissive())
            .with_state(state);

        println!("✓ 启动 HTTP 服务器...");
        axum::serve(
            tokio::net::TcpListener::bind(http_addr).await.unwrap(),
            app.into_make_service()
        )
            .await
            .unwrap();
    });

    // 6. 启动 Thrift 服务器
    tokio::spawn(async move {
        let read_transport_factory = TBufferedReadTransportFactory::new();
        let write_transport_factory = TBufferedWriteTransportFactory::new();
        let input_protocol_factory = TBinaryInputProtocolFactory::new();
        let output_protocol_factory = TBinaryOutputProtocolFactory::new();

        let mut thrift_server = TServer::new(
            read_transport_factory,
            input_protocol_factory,
            write_transport_factory,
            output_protocol_factory,
            thrift_processor,
            1,
        );

        println!("✓ 启动 Thrift 服务器...");
        if let Err(e) = thrift_server.listen(thrift_addr) {
            eprintln!("Thrift 服务器错误: {}", e);
        }
    });

    // 7. 启动 gRPC 服务器（主线程）
    println!("✓ 启动 gRPC 服务器...");
    // 注意：这里需要实际的 gRPC 服务实现
    // tonic::transport::Server::builder()
    //     .add_service(grpc_service)
    //     .serve(grpc_addr)
    //     .await?;

    // 临时解决方案：让主线程保持运行
    println!("✓ 所有服务器已启动，按 Ctrl+C 停止");
    tokio::signal::ctrl_c().await?;
    println!("✓ 收到停止信号，正在关闭服务器...");

    Ok(())
}

// 修改 main 函数
#[tokio::main]
#[cfg(feature = "server")]
async fn main() -> Result<(), Box<dyn Error>> {
    run_servers().await
}



