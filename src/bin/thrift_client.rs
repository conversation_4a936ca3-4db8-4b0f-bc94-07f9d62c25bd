use rust_demo::proto_gen::unique_id_service::{TUniqueIdServiceSyncClient, UniqueIdServiceSyncClient};
use thrift::protocol::{TBinaryInputProtocol, TBinaryOutputProtocol};
use thrift::transport::{TBufferedReadTransport, TBufferedWriteTransport, TTcpChannel};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Connecting to Thrift UniqueId Service...");

    // Connect to the server
    let mut channel = TTcpChannel::new();
    channel.open("127.0.0.1:9090")?;

    let (i_chan, o_chan) = channel.split()?;
    let i_tran = TBufferedReadTransport::new(i_chan);
    let o_tran = TBufferedWriteTransport::new(o_chan);
    let i_prot = TBinaryInputProtocol::new(i_tran, true);
    let o_prot = TBinaryOutputProtocol::new(o_tran, true);

    let mut client = UniqueIdServiceSyncClient::new(i_prot, o_prot);

    println!("Connected successfully!");

    // Test single ID generation
    println!("\n=== Testing single ID generation ===");
    for i in 1..=5 {
        match client.get() {
            Ok(id) => println!("Generated ID {}: {}", i, id),
            Err(e) => println!("Error generating ID {}: {:?}", i, e),
        }
    }

    // Test multiple ID generation
    println!("\n=== Testing multiple ID generation ===");
    let test_cases = vec![1, 3, 5, 10];
    
    for num in test_cases {
        match client.gets(num) {
            Ok(ids) => {
                println!("Generated {} IDs: {:?}", num, ids);
                
                // Verify all IDs are unique
                let mut sorted_ids = ids.clone();
                sorted_ids.sort();
                sorted_ids.dedup();
                if sorted_ids.len() == ids.len() {
                    println!("✓ All {} IDs are unique", num);
                } else {
                    println!("✗ Found duplicate IDs!");
                }
            }
            Err(e) => println!("Error generating {} IDs: {:?}", num, e),
        }
    }

    // Test error cases
    println!("\n=== Testing error cases ===");
    
    // Test negative number
    match client.gets(-1) {
        Ok(_) => println!("✗ Expected error for negative number, but got success"),
        Err(e) => println!("✓ Got expected error for negative number: {:?}", e),
    }

    // Test zero
    match client.gets(0) {
        Ok(_) => println!("✗ Expected error for zero, but got success"),
        Err(e) => println!("✓ Got expected error for zero: {:?}", e),
    }

    // Test too many IDs
    match client.gets(1001) {
        Ok(_) => println!("✗ Expected error for too many IDs, but got success"),
        Err(e) => println!("✓ Got expected error for too many IDs: {:?}", e),
    }

    println!("\n=== Client test completed ===");
    Ok(())
}
