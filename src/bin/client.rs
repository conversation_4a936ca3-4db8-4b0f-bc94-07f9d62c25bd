use unique_id_service::proto_gen::unique_id_service::{TUniqueIdServiceSyncClient, UniqueIdServiceSyncClient};
use std::env;
use thrift::protocol::{TBinaryInputProtocol, TBinaryOutputProtocol};
use thrift::transport::{TBufferedReadTransport, TBufferedWriteTransport, TTcpChannel, TIoChannel};
use log::{info, error};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logger
    env_logger::init();

    info!("Starting Thrift UniqueId Service Client...");

    // Parse command line arguments
    let args: Vec<String> = env::args().collect();
    let server_address = if args.len() > 1 {
        args[1].clone()
    } else {
        "127.0.0.1:9090".to_string()
    };

    info!("Connecting to Thrift UniqueId Service at {}...", server_address);

    // Connect to the server
    let mut channel = TTcpChannel::new();
    channel.open(&server_address)?;

    let (i_chan, o_chan) = channel.split()?;
    let i_tran = TBufferedReadTransport::new(i_chan);
    let o_tran = TBufferedWriteTransport::new(o_chan);
    let i_prot = TBinaryInputProtocol::new(i_tran, true);
    let o_prot = TBinaryOutputProtocol::new(o_tran, true);

    let mut client = UniqueIdServiceSyncClient::new(i_prot, o_prot);

    info!("Connected successfully!");

    // Test single ID generation
    println!("\n=== Testing single ID generation ===");
    for i in 1..=5 {
        match client.get() {
            Ok(id) => {
                println!("Generated ID {}: {}", i, id);
                info!("Generated ID {}: {}", i, id);
            }
            Err(e) => {
                println!("Error generating ID {}: {:?}", i, e);
                error!("Error generating ID {}: {:?}", i, e);
            }
        }
    }

    // Test multiple ID generation
    println!("\n=== Testing multiple ID generation ===");
    let test_cases = vec![1, 3, 5, 10, 50];
    
    for num in test_cases {
        match client.gets(num) {
            Ok(ids) => {
                println!("Generated {} IDs: {:?}", num, ids);
                info!("Generated {} IDs successfully", num);
                
                // Verify all IDs are unique
                let mut sorted_ids = ids.clone();
                sorted_ids.sort();
                sorted_ids.dedup();
                if sorted_ids.len() == ids.len() {
                    println!("✓ All {} IDs are unique", num);
                } else {
                    println!("✗ Found duplicate IDs!");
                    error!("Found duplicate IDs in batch of {}", num);
                }
            }
            Err(e) => {
                println!("Error generating {} IDs: {:?}", num, e);
                error!("Error generating {} IDs: {:?}", num, e);
            }
        }
    }

    // Test error cases
    println!("\n=== Testing error cases ===");
    
    // Test negative number
    match client.gets(-1) {
        Ok(_) => {
            println!("✗ Expected error for negative number, but got success");
            error!("Expected error for negative number, but got success");
        }
        Err(e) => {
            println!("✓ Got expected error for negative number: {:?}", e);
            info!("Got expected error for negative number");
        }
    }

    // Test zero
    match client.gets(0) {
        Ok(_) => {
            println!("✗ Expected error for zero, but got success");
            error!("Expected error for zero, but got success");
        }
        Err(e) => {
            println!("✓ Got expected error for zero: {:?}", e);
            info!("Got expected error for zero");
        }
    }

    // Test too many IDs
    match client.gets(1001) {
        Ok(_) => {
            println!("✗ Expected error for too many IDs, but got success");
            error!("Expected error for too many IDs, but got success");
        }
        Err(e) => {
            println!("✓ Got expected error for too many IDs: {:?}", e);
            info!("Got expected error for too many IDs");
        }
    }

    // Performance test
    println!("\n=== Performance test ===");
    let start = std::time::Instant::now();
    let batch_size = 100;
    let num_batches = 10;
    
    for i in 0..num_batches {
        match client.gets(batch_size) {
            Ok(ids) => {
                println!("Batch {}: Generated {} IDs", i + 1, ids.len());
            }
            Err(e) => {
                println!("Batch {}: Error: {:?}", i + 1, e);
                error!("Performance test batch {} failed: {:?}", i + 1, e);
            }
        }
    }
    
    let duration = start.elapsed();
    let total_ids = batch_size * num_batches;
    let ids_per_second = total_ids as f64 / duration.as_secs_f64();
    
    println!("Performance: Generated {} IDs in {:?}", total_ids, duration);
    println!("Rate: {:.2} IDs/second", ids_per_second);
    info!("Performance test completed: {:.2} IDs/second", ids_per_second);

    println!("\n=== Client test completed ===");
    info!("Client test completed successfully");
    Ok(())
}
