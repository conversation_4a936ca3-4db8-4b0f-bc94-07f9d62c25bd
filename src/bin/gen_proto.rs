use std::error::Error;
use std::fs;
use std::path::PathBuf;

static OUT_DIR: &str = "src/proto-gen/grpc";

fn get_absolute_path(relative_path: &str) -> std::io::Result<PathBuf> {
    std::fs::canonicalize(relative_path)
}

fn rerun(proto_files: &[&str]) {
    for proto_file in proto_files {
        println!("cargo:rerun-if-changed={}", proto_file);
    }
}

#[cfg(feature = "gen_proto")]
fn main() -> Result<(), Box<dyn Error>> {
    let protos = [
        "/Users/<USER>/data/src/ponderers.org/ailurus/protocol/grpc/totoro_infrastructure/unique_id_service.proto",
    ];
    fs::create_dir_all(OUT_DIR).unwrap();
    tonic_build::configure()
        .build_server(true)
        .out_dir(OUT_DIR)
        .compile(
            &protos, 
            &["/Users/<USER>/data/src/ponderers.org/ailurus/protocol/grpc/totoro_infrastructure/"]
        )?;
    rerun(&protos);
    Ok(())
}