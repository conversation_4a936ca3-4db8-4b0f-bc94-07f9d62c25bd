use rust_demo::proto_gen::unique_id_service::UniqueIdServiceSyncProcessor;
use rust_demo::services::unique_id_thrift_service::UniqueIdThriftService;
use std::sync::Arc;
use thrift::protocol::{TBinaryInputProtocol, TBinaryOutputProtocol};
use thrift::server::TServer;
use thrift::transport::{
    TBufferedReadTransport, TBufferedWriteTransport, TIoChannel, TReadTransport, TServerSocket,
    TTcpChannel, TWriteTransport,
};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("Starting Thrift UniqueId Service Server...");

    // Create the service implementation
    let service = UniqueIdThriftService::with_default_worker_id()
        .map_err(|e| format!("Failed to create service: {}", e))?;

    // Create the processor
    let processor = UniqueIdServiceSyncProcessor::new(service);

    // Create server socket
    let listen_address = "127.0.0.1:9090";
    println!("Server listening on {}", listen_address);

    let mut server = TServer::new(
        TServerSocket::new(listen_address)?,
        processor,
        |stream| {
            let transport = TBufferedReadTransport::new(stream);
            let transport = TBufferedWriteTransport::new(transport);
            let i_prot = TBinaryInputProtocol::new(transport.clone(), true);
            let o_prot = TBinaryOutputProtocol::new(transport, true);
            (i_prot, o_prot)
        },
    );

    println!("UniqueId Thrift Server started successfully!");
    server.listen()
}
