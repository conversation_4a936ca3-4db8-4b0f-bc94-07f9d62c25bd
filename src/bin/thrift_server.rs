use rust_demo::unique_id_service::thrift::UniqueIdServiceSyncProcessor;
use rust_demo::services::unique_id_thrift_service::UniqueIdThriftService;
use std::env;
use std::error::Error;
use thrift::protocol::{TBinaryInputProtocolFactory, TBinaryOutputProtocolFactory};
use thrift::server::TServer;
use thrift::transport::{TBufferedReadTransportFactory, TBufferedWriteTransportFactory};

fn main() -> Result<(), Box<dyn Error>> {
    println!("=== Starting Thrift UniqueId Service Server ===");

    // 解析命令行参数
    let args: Vec<String> = env::args().collect();
    let worker_id = if args.len() > 1 {
        args[1].parse::<u64>().unwrap_or_else(|_| {
            eprintln!("Invalid worker_id: {}, using default", args[1]);
            std::process::id() as u64 % 1024
        })
    } else {
        std::process::id() as u64 % 1024
    };

    let listen_address = if args.len() > 2 {
        args[2].clone()
    } else {
        "127.0.0.1:9090".to_string()
    };

    println!("Worker ID: {}", worker_id);
    println!("Listen Address: {}", listen_address);

    // 创建服务实现
    let service = UniqueIdThriftService::new(worker_id)
        .map_err(|e| format!("Failed to create service: {}", e))?;

    println!("✓ Created UniqueIdThriftService with worker_id: {}", worker_id);

    // 创建处理器
    let processor = UniqueIdServiceSyncProcessor::new(service);

    // 创建传输和协议工厂
    let read_transport_factory = TBufferedReadTransportFactory::new();
    let write_transport_factory = TBufferedWriteTransportFactory::new();
    let input_protocol_factory = TBinaryInputProtocolFactory::new();
    let output_protocol_factory = TBinaryOutputProtocolFactory::new();

    // 创建服务器
    let mut server = TServer::new(
        read_transport_factory,
        input_protocol_factory,
        write_transport_factory,
        output_protocol_factory,
        processor,
        1, // num_workers
    );

    println!("✓ Thrift server configured successfully");
    println!("🚀 Starting server on {}...", listen_address);
    println!();
    println!("Usage:");
    println!("  cargo run --bin thrift_server [worker_id] [listen_address]");
    println!("  Example: cargo run --bin thrift_server 1 127.0.0.1:9090");
    println!();
    println!("API:");
    println!("  - get(): Generate single unique ID");
    println!("  - gets(num): Generate multiple unique IDs (1-1000)");
    println!();

    // 启动服务器
    match server.listen(&listen_address) {
        Ok(_) => {
            println!("✓ Server stopped gracefully");
            Ok(())
        }
        Err(e) => {
            eprintln!("✗ Server error: {}", e);
            Err(Box::new(e))
        }
    }
}
