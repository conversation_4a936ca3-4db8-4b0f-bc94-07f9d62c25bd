use std::env;

pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub min_connections: u32,
}

impl DatabaseConfig {
    pub fn new() -> Self {
        // 从环境变量获取运行环境
        let env_file = match env::var("CONFIG_ENV").unwrap_or_else(|_| "dev".to_string()).as_str() {
            "prod" => ".env.prod",
            "test" => ".env.test",
            _ => ".env.dev"
        };
        // CONFIG_ENV=prod
        dotenv::from_filename(env_file).ok();
        // 实际项目中应该从环境变量或配置文件读取
        Self {
            url: env::var("DATABASE_URL").expect("DB_HOST must be set"),
            max_connections: 100,
            min_connections: 5,
        }
    }
}