use serde::Serialize;
use chrono::{DateTime, NaiveDateTime};
use crate::entities::student;

#[derive(Debug, Serialize)]
pub struct StudentVo {
    pub gid: i64,
    pub nickname: String,
    pub age: i32,
    #[serde(rename = "idCard")]  // 转换字段名为驼峰命名
    pub id_card: String,
    #[serde(rename = "updateTime")]
    pub update_time: sea_orm::prelude::DateTime,     // 转换为字符串格式
    pub create_time: sea_orm::prelude::DateTime,
    // 可以添加计算字段
    pub age_group: String,       // 根据年龄计算的分组
}

impl From<student::Model> for StudentVo {
    fn from(model: student::Model) -> Self {
        Self {
            gid: model.gid,
            nickname: model.nickname,
            age: model.age,
            id_card: model.id_card,
            // 格式化日期时间
            update_time: model.update_time,
            create_time: model.create_time,
            // 计算年龄分组
            age_group: match model.age {
                0..=18 => "少年".to_string(),
                19..=35 => "青年".to_string(),
                36..=60 => "中年".to_string(),
                _ => "老年".to_string(),
            },
        }
    }
}

// 批量转换的辅助方法
impl StudentVo {
    pub fn from_models(models: Vec<student::Model>) -> Vec<Self> {
        models.into_iter().map(Self::from).collect()
    }
}