use serde::Deserialize;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Deserialize)]
pub struct Settings {
    pub server: Server,
    pub database: DatabaseConfig
}

#[derive(Debug, <PERSON><PERSON>, Deserialize)]
pub struct Server {
    pub host: String,
    pub port: u16,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Deserialize)]
pub enum DatabaseType {
    MySQL,
    PostgreSQL,
    SQLite,
}

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Deserialize)]
pub struct DatabaseConfig {
    pub db_type: DatabaseType,
    pub host: String,
    pub port: u16,
    pub database: String,
    pub max_connections: u32,
    pub username: String,
    pub password: String,
    pub ssl_mode: Option<String>,
}

impl DatabaseConfig {
    pub fn build_url(&self) -> String {
        match self.db_type {
            DatabaseType::MySQL => {
                format!(
                    "mysql://{}:{}@{}:{}/{}",
                    self.username, self.password, self.host, self.port, self.database
                )
            }
            DatabaseType::PostgreSQL => {
                let ssl_mode = self.ssl_mode.as_deref().unwrap_or("prefer");
                format!(
                    "postgresql://{}:{}@{}:{}/{}?sslmode={}",
                    self.username, self.password, self.host, self.port, self.database, ssl_mode
                )
            }
            DatabaseType::SQLite => {
                format!("sqlite://{}?mode=rwc", self.database)
            }
        }
    }
}
