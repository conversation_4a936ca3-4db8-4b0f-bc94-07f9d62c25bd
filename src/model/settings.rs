use serde::Deserialize;

#[derive(Debug, Deserialize)]
pub struct Settings {
    pub server: Server,
    pub database: DatabaseConfig,
}

#[derive(Debug, Deserialize)]
pub struct Server {
    pub host: String,
    pub port: u16,
}

#[derive(Debug, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub username: String,
    pub password: String,
}

impl DatabaseConfig {
    pub(crate) fn build_url(&self) -> _ {
        todo!()
    }
}
