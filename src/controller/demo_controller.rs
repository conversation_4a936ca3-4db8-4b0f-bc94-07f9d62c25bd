use crate::client::global_id_grpc_client::gen_global_id;
use crate::model::app_status::AppState;
use crate::model::student_vo::StudentVo;
use axum::extract::Query;
use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use serde::{Deserialize, Serialize};

#[derive(Serialize)]
pub struct ApiResponse<T> {
    pub code: i32,
    pub message: String,
    pub data: Option<T>,
}

pub async fn get_global_id() -> impl IntoResponse {
    let result = gen_global_id().await;
    let f = result.unwrap_or_else(|error| {
        print!("Problem opening the file: {:?}", error);
        0
    });
    Json(ApiResponse {
        code: 0,
        message: "success".to_string(),
        data: Some(f),
    }).into_response()
}

pub async fn get_student_by_gid(State(state): State<AppState>, Path(gid): Path<i64>) -> impl IntoResponse {
    match state.student_repo.find_by_id(gid).await {
        Ok(Some(student)) => Json(ApiResponse {
            code: 0,
            message: "success".to_string(),
            data: Some(StudentVo::from(student)),
        }).into_response(),
        Ok(None) => Json(ApiResponse::<StudentVo> {
            code: 404,
            message: "student not found".to_string(),
            data: None,
        }).into_response(),
        Err(_) => StatusCode::INTERNAL_SERVER_ERROR.into_response(),
    }
}

#[derive(Deserialize)]
pub struct Pagination {
    page: Option<usize>,
    page_size: Option<usize>,
}

impl Pagination {
    /// 计算分页的 offset 和 limit（提供默认值）
    pub fn calculate_offset_limit(&self) -> (u64, u64) {
        let page = self.page.unwrap_or(1); // 默认第 1 页
        let page_size = self.page_size.unwrap_or(10); // 默认每页 10 条
        let offset = (page - 1) * page_size;
        (offset as u64, page_size as u64)
    }

    /// 检查分页参数是否有效
    pub fn is_valid(&self) -> bool {
        self.page.unwrap_or(1) > 0 && self.page_size.unwrap_or(10) > 0
    }
}

pub async fn get_student_by_age(State(state): State<AppState>, pagination: Query<Pagination>) -> impl IntoResponse {
    let result = state.student_repo.find_by_age(pagination).await;
    let students = result.unwrap();
    println!("query student by age: {:?}", students);
    Json(ApiResponse {
        code: 0,
        message: "success".to_string(),
        data: Some(students),
    }).into_response()
}

pub async fn new_student(State(state): State<AppState>) -> impl IntoResponse {
    let affected = state.student_repo.insert().await;
    println!("Created student: {:?}", affected);
    Json(ApiResponse {
        code: 0,
        message: "success".to_string(),
        data: Some("f"),
    }).into_response()
}

pub async fn update_student(State(state): State<AppState>) -> impl IntoResponse {
    let student = state.student_repo.find_first().await;
    println!("Students younger than 200: {:?}", student);
    let affected = state.student_repo.update("王麻子", student.unwrap().unwrap().gid).await;
    println!("Updated student: {:?}", affected);
    Json(ApiResponse {
        code: 0,
        message: "success".to_string(),
        data: Some("f"),
    }).into_response()
}
