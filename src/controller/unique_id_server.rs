use tonic::{Request as Toni<PERSON><PERSON>e<PERSON>, Response as <PERSON><PERSON><PERSON><PERSON>po<PERSON>, Status};
use c r::totoro_infrastructure::global_id_server::{GlobalId, GlobalIdServer};
use rust_demo::totoro_infrastructure::{GetResponse, GetsRequest, GetsResponse};

#[derive(Default)]
pub struct UniqueIdService {}

#[tonic::async_trait]
impl UniqueId for UniqueIdService {
    async fn get(&self, request: TonicRequest<()>) -> std::result::Result<TonicResponse<GetResponse>, Status> {
        println!("receive request: {:?}", request);
        let response = GetResponse {
            global_id: 3,
        };
        Ok(TonicResponse::new(response))
    }

    async fn gets(&self, request: TonicRequest<GetsRequest>) -> std::result::Result<TonicResponse<GetsResponse>, Status> {
        println!("receive request: {:?}", request);
        let response = GetsResponse {
            global_id: vec![1, 2],
        };
        Ok(TonicResponse::new(response))
    }
}