use tonic::{Request as <PERSON><PERSON><PERSON><PERSON><PERSON>, Response as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Status};
use crate::totoro_infrastructure ::{UniqueId, GetResponse, GetsRequest, GetsResponse};

#[derive(Default)]
pub struct UniqueIdService {}

#[tonic::async_trait]
impl UniqueId for UniqueIdService {
    async fn get(&self, request: TonicRequest<()>) -> std::result::Result<TonicResponse<GetResponse>, Status> {
        println!("receive request: {:?}", request);
        let response = GetResponse {
            global_id: 3,
        };
        Ok(TonicResponse::new(response))
    }

    async fn gets(&self, request: TonicRequest<GetsRequest>) -> std::result::Result<TonicResponse<GetsResponse>, Status> {
        println!("receive request: {:?}", request);
        let response = GetsResponse {
            global_id: vec![1, 2],
        };
        Ok(TonicResponse::new(response))
    }
}