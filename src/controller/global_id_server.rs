use tonic::{Request as <PERSON><PERSON><PERSON><PERSON><PERSON>, Response as <PERSON><PERSON><PERSON><PERSON>po<PERSON>, Status};
use crate::totoro_infrastructure::{GetResponse, GetsRequest, GetsResponse};
use crate::totoro_infrastructure::unique_id_service_server::UniqueIdService;

#[derive(Default)]
pub struct GlobalIdService {}

#[tonic::async_trait]
impl UniqueIdService for GlobalIdService {
    async fn get(&self, request: TonicRequest<()>) -> std::result::Result<TonicResponse<GetResponse>, Status> {
        println!("receive request: {:?}", request);
        let response = GetResponse {
            unique_id: 3,
        };
        Ok(TonicResponse::new(response))
    }

    async fn gets(&self, request: TonicRequest<GetsRequest>) -> std::result::Result<TonicResponse<GetsResponse>, Status> {
        println!("receive request: {:?}", request);
        let response = GetsResponse {
            unique_id: vec![1, 2],
        };
        Ok(TonicResponse::new(response))
    }
}