// 原有的 gRPC 协议模块（如果存在）
// pub mod totoro_infrastructure {
//     include!("./proto-gen/totoro_infrastructure.rs");
// }

// 生成的 thrift 协议模块
pub mod unique_id_service {
    #[allow(dead_code)]
    #[allow(unused_imports)]
    #[allow(unused_extern_crates)]
    #[allow(clippy::too_many_arguments, clippy::type_complexity, clippy::vec_box, clippy::wrong_self_convention)]
    #[cfg_attr(rustfmt, rustfmt_skip)]
    pub mod thrift {
        include!("./proto-gen/unique_id_service.rs");
    }
}

// 原有模块
pub mod config;
pub mod db;
pub mod entities;
pub mod error;
pub mod dao;
pub mod model;
pub mod controller;
pub mod client;

// 服务实现模块
pub mod services;