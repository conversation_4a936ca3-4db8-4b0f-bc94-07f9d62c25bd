use tonic::transport::Endpoint;
use crate::totoro_infrastructure::GetsRequest;
use crate::totoro_infrastructure::unique_id_client::UniqueIdClient;

pub async fn gen_global_id() -> Result<i64, Box<dyn std::error::Error>> {
    let addr = Endpoint::from_static("http://10.211.56.2:9090");
    
    let result = UniqueIdClient::connect(addr.clone()).await;
    if result.is_err() {
        return Err(result.err().unwrap().into());
    }
    
    let mut client = result.unwrap();
    let request = GetsRequest {
        num: 1,
    };
    let result = client.gets(request).await.unwrap();
    let response = result.into_inner();
    println!("gets_response: {:?}", response);
    let global_ids = response.global_id;
    println!("global_ids: {:?}", global_ids);
    
    // 对于 get 请求
    let response = client.get(()).await.unwrap();
    let get_response = response.into_inner();
    println!("get_response: {:?}", get_response);
    println!("global_id: {:?}", get_response.global_id);
    Ok(get_response.global_id)
}