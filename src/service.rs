use crate::proto_gen::unique_id_service::UniqueIdServiceSyncHandler;
use crate::snowflake::SnowflakeIdGenerator;
use std::sync::Arc;
use log::{info, warn, error};

/// UniqueId service implementation using Snowflake algorithm
pub struct UniqueIdService {
    id_generator: Arc<SnowflakeIdGenerator>,
}

impl UniqueIdService {
    pub fn new(worker_id: u64) -> Result<Self, String> {
        let id_generator = Arc::new(SnowflakeIdGenerator::new(worker_id)?);
        info!("UniqueIdService created with worker_id: {}", worker_id);
        Ok(UniqueIdService { id_generator })
    }

    pub fn with_default_worker_id() -> Result<Self, String> {
        let id_generator = Arc::new(SnowflakeIdGenerator::with_default_worker_id()?);
        info!("UniqueIdService created with default worker_id");
        Ok(UniqueIdService { id_generator })
    }
}

impl UniqueIdServiceSyncHandler for UniqueIdService {
    fn handle_get(&self) -> thrift::Result<i64> {
        info!("Handling get() request");
        
        match self.id_generator.next_id() {
            Ok(id) => {
                info!("Generated ID: {}", id);
                Ok(id)
            }
            Err(e) => {
                error!("Failed to generate ID: {}", e);
                Err(thrift::Error::Application(thrift::ApplicationError::new(
                    thrift::ApplicationErrorKind::InternalError,
                    format!("Failed to generate ID: {}", e),
                )))
            }
        }
    }

    fn handle_gets(&self, num: i32) -> thrift::Result<Vec<i64>> {
        info!("Handling gets({}) request", num);

        // Validate input
        if num <= 0 {
            warn!("Invalid request: num must be positive, got {}", num);
            return Err(thrift::Error::Application(thrift::ApplicationError::new(
                thrift::ApplicationErrorKind::InvalidMessageType,
                "Number of IDs must be positive".to_string(),
            )));
        }

        if num > 1000 {
            warn!("Invalid request: num too large, got {}", num);
            return Err(thrift::Error::Application(thrift::ApplicationError::new(
                thrift::ApplicationErrorKind::InvalidMessageType,
                "Cannot generate more than 1000 IDs at once".to_string(),
            )));
        }

        // Generate IDs
        match self.id_generator.generate_batch(num as usize) {
            Ok(ids) => {
                info!("Generated {} IDs successfully", ids.len());
                Ok(ids)
            }
            Err(e) => {
                error!("Failed to generate {} IDs: {}", num, e);
                Err(thrift::Error::Application(thrift::ApplicationError::new(
                    thrift::ApplicationErrorKind::InternalError,
                    format!("Failed to generate IDs: {}", e),
                )))
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_unique_id_service() {
        let service = UniqueIdService::new(1).unwrap();
        
        // Test single ID generation
        let id = service.handle_get().unwrap();
        assert!(id > 0);
        
        // Test multiple ID generation
        let ids = service.handle_gets(5).unwrap();
        assert_eq!(ids.len(), 5);
        
        // All IDs should be unique
        for i in 0..ids.len() {
            for j in i+1..ids.len() {
                assert_ne!(ids[i], ids[j]);
            }
        }
    }

    #[test]
    fn test_invalid_requests() {
        let service = UniqueIdService::new(1).unwrap();
        
        // Test negative number
        assert!(service.handle_gets(-1).is_err());
        
        // Test zero
        assert!(service.handle_gets(0).is_err());
        
        // Test too many IDs
        assert!(service.handle_gets(1001).is_err());
    }

    #[test]
    fn test_service_creation() {
        // Test with valid worker ID
        let service = UniqueIdService::new(100);
        assert!(service.is_ok());
        
        // Test with invalid worker ID
        let service = UniqueIdService::new(1024);
        assert!(service.is_err());
        
        // Test with default worker ID
        let service = UniqueIdService::with_default_worker_id();
        assert!(service.is_ok());
    }
}
