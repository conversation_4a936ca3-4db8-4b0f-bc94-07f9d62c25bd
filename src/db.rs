use std::time::Duration;
use sea_orm::*;
use crate::error::AppResult;
use crate::model::settings::DatabaseConfig;

pub async fn establish_connection(config: &DatabaseConfig) -> AppResult<DatabaseConnection> {
    // let conn = Database::connect(&config.url).await?;
    // Ok(conn)
    let mut opt = ConnectOptions::new(&config.build_url());
    opt
        .max_connections(100)
        .min_connections(5)
        .connect_timeout(Duration::from_secs(8))
        .acquire_timeout(Duration::from_secs(8))
        .idle_timeout(Duration::from_secs(8))
        .max_lifetime(Duration::from_secs(8))
        .sqlx_logging(true)
        .sqlx_logging_level(log::LevelFilter::Info);

    let conn = Database::connect(opt).await?;
    Ok(conn)
}