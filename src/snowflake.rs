use std::sync::{<PERSON><PERSON><PERSON>, Arc};
use std::time::{SystemTime, UNIX_EPOCH};

/// Snowflake ID generator implementation
///
/// Snowflake ID format (64 bits):
/// - 1 bit: unused (always 0)
/// - 41 bits: timestamp (milliseconds since custom epoch)
/// - 10 bits: machine/worker ID
/// - 12 bits: sequence number
#[derive(Debug)]
pub struct SnowflakeIdGenerator {
    /// Worker ID (0-1023)
    worker_id: u64,
    /// Internal state protected by mutex
    state: Arc<Mutex<SnowflakeState>>,
    /// Custom epoch (2020-01-01 00:00:00 UTC)
    epoch: u64,
}

#[derive(Debug)]
struct SnowflakeState {
    /// Sequence counter for the current millisecond
    sequence: u64,
    /// Last timestamp when ID was generated
    last_timestamp: u64,
}

impl SnowflakeIdGenerator {
    const WORKER_ID_BITS: u64 = 10;
    const SEQUENCE_BITS: u64 = 12;
    const MAX_WORKER_ID: u64 = (1 << Self::WORKER_ID_BITS) - 1;
    const MAX_SEQUENCE: u64 = (1 << Self::SEQUENCE_BITS) - 1;
    const WORKER_ID_SHIFT: u64 = Self::SEQUENCE_BITS;
    const TIMESTAMP_SHIFT: u64 = Self::SEQUENCE_BITS + Self::WORKER_ID_BITS;

    pub fn new(worker_id: u64) -> Result<Self, String> {
        if worker_id > Self::MAX_WORKER_ID {
            return Err(format!("Worker ID must be between 0 and {}", Self::MAX_WORKER_ID));
        }

        // Custom epoch: 2020-01-01 00:00:00 UTC
        let epoch = 1577836800000; // milliseconds

        Ok(SnowflakeIdGenerator {
            worker_id,
            sequence: AtomicU64::new(0),
            last_timestamp: AtomicU64::new(0),
            epoch,
        })
    }

    pub fn with_default_worker_id() -> Result<Self, String> {
        // Use a default worker ID based on process ID or random value
        let worker_id = std::process::id() as u64 % (Self::MAX_WORKER_ID + 1);
        Self::new(worker_id)
    }

    fn current_timestamp(&self) -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_millis() as u64
    }

    pub fn next_id(&self) -> Result<i64, String> {
        loop {
            let mut timestamp = self.current_timestamp();
            let last_timestamp = self.last_timestamp.load(Ordering::SeqCst);

            if timestamp < last_timestamp {
                return Err("Clock moved backwards".to_string());
            }

            let sequence = if timestamp == last_timestamp {
                let seq = self.sequence.fetch_add(1, Ordering::SeqCst);
                if seq > Self::MAX_SEQUENCE {
                    // Wait for next millisecond
                    std::thread::sleep(std::time::Duration::from_millis(1));
                    continue;
                } else {
                    seq
                }
            } else {
                self.sequence.store(0, Ordering::SeqCst);
                0
            };

            // Try to update the timestamp atomically
            match self.last_timestamp.compare_exchange_weak(
                last_timestamp,
                timestamp,
                Ordering::SeqCst,
                Ordering::SeqCst,
            ) {
                Ok(_) => {
                    let id = ((timestamp - self.epoch) << Self::TIMESTAMP_SHIFT)
                        | (self.worker_id << Self::WORKER_ID_SHIFT)
                        | sequence;

                    return Ok(id as i64);
                }
                Err(_) => {
                    // Another thread updated the timestamp, retry
                    continue;
                }
            }
        }
    }

    pub fn generate_batch(&self, count: usize) -> Result<Vec<i64>, String> {
        let mut ids = Vec::with_capacity(count);
        for _ in 0..count {
            ids.push(self.next_id()?);
        }
        Ok(ids)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_snowflake_id_generator() {
        let generator = SnowflakeIdGenerator::new(1).unwrap();
        
        // Generate some IDs
        let id1 = generator.next_id().unwrap();
        let id2 = generator.next_id().unwrap();
        
        // IDs should be different
        assert_ne!(id1, id2);
        
        // IDs should be positive
        assert!(id1 > 0);
        assert!(id2 > 0);
        
        // Second ID should be greater than first (assuming same millisecond)
        assert!(id2 > id1);
    }

    #[test]
    fn test_batch_generation() {
        let generator = SnowflakeIdGenerator::new(1).unwrap();
        
        let ids = generator.generate_batch(10).unwrap();
        assert_eq!(ids.len(), 10);
        
        // All IDs should be unique
        for i in 0..ids.len() {
            for j in i+1..ids.len() {
                assert_ne!(ids[i], ids[j]);
            }
        }
    }

    #[test]
    fn test_invalid_worker_id() {
        let result = SnowflakeIdGenerator::new(1024);
        assert!(result.is_err());
    }
}
