use rust_demo::unique_id_service::thrift::UniqueIdServiceSyncHandler;
use rust_demo::services::unique_id_thrift_service::UniqueIdThriftService;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== UniqueId Service Example ===");

    // 创建服务实例
    let service = UniqueIdThriftService::new(1)?;
    println!("Created UniqueIdThriftService with worker_id: 1");

    // 测试单个 ID 生成
    println!("\n--- Testing single ID generation ---");
    for i in 1..=5 {
        match service.handle_get() {
            Ok(id) => println!("Generated ID {}: {}", i, id),
            Err(e) => println!("Error generating ID {}: {:?}", i, e),
        }
    }

    // 测试批量 ID 生成
    println!("\n--- Testing batch ID generation ---");
    let batch_sizes = vec![1, 5, 10, 50];
    
    for batch_size in batch_sizes {
        match service.handle_gets(batch_size) {
            Ok(ids) => {
                println!("Generated {} IDs: {:?}", batch_size, ids);
                
                // 验证所有 ID 都是唯一的
                let mut sorted_ids = ids.clone();
                sorted_ids.sort();
                sorted_ids.dedup();
                if sorted_ids.len() == ids.len() {
                    println!("✓ All {} IDs are unique", batch_size);
                } else {
                    println!("✗ Found duplicate IDs!");
                }
            }
            Err(e) => println!("Error generating {} IDs: {:?}", batch_size, e),
        }
    }

    // 测试错误情况
    println!("\n--- Testing error cases ---");
    
    // 测试负数
    match service.handle_gets(-1) {
        Ok(_) => println!("✗ Expected error for negative number, but got success"),
        Err(e) => println!("✓ Got expected error for negative number: {:?}", e),
    }

    // 测试零
    match service.handle_gets(0) {
        Ok(_) => println!("✗ Expected error for zero, but got success"),
        Err(e) => println!("✓ Got expected error for zero: {:?}", e),
    }

    // 测试过大的数量
    match service.handle_gets(1001) {
        Ok(_) => println!("✗ Expected error for too many IDs, but got success"),
        Err(e) => println!("✓ Got expected error for too many IDs: {:?}", e),
    }

    // 性能测试
    println!("\n--- Performance test ---");
    let start = std::time::Instant::now();
    let batch_size = 100;
    let num_batches = 10;
    
    for i in 0..num_batches {
        match service.handle_gets(batch_size) {
            Ok(ids) => {
                println!("Batch {}: Generated {} IDs", i + 1, ids.len());
            }
            Err(e) => {
                println!("Batch {}: Error: {:?}", i + 1, e);
            }
        }
    }
    
    let duration = start.elapsed();
    let total_ids = batch_size * num_batches;
    let ids_per_second = total_ids as f64 / duration.as_secs_f64();
    
    println!("Performance: Generated {} IDs in {:?}", total_ids, duration);
    println!("Rate: {:.2} IDs/second", ids_per_second);

    println!("\n=== Example completed successfully ===");
    Ok(())
}
