# 配置管理系统

## 🔧 **问题解决**

### **原始问题**
```
configuration file "../config/dev" not found
```

### **解决方案**
1. **修复配置文件路径**: 从 `../config/dev` 改为 `src/config/{env}.toml`
2. **统一配置文件格式**: 所有配置文件使用相同的结构
3. **支持多环境**: dev、prod、test、default

## 📁 **配置文件结构**

```
src/config/
├── default.toml    # 默认配置
├── dev.toml        # 开发环境配置
├── prod.toml       # 生产环境配置
├── test.toml       # 测试环境配置
└── mod.rs          # 模块声明
```

## 🔧 **配置文件格式**

所有配置文件都使用统一的 TOML 格式：

```toml
[server]
host = "127.0.0.1"
port = 3000

[database]
db_type = "MySQL"           # MySQL, PostgreSQL, SQLite
host = "localhost"
port = 3306
database = "ailurus"
username = "root"
password = ""
max_connections = 10
```

## 🚀 **使用方法**

### **1. 默认环境 (dev)**
```bash
cargo run --bin server --features server
# 加载: src/config/dev.toml
```

### **2. 生产环境**
```bash
RUN_MODE=prod cargo run --bin server --features server
# 加载: src/config/prod.toml
```

### **3. 测试环境**
```bash
RUN_MODE=test cargo run --bin server --features server
# 加载: src/config/test.toml
```

### **4. 自定义环境**
```bash
RUN_MODE=staging cargo run --bin server --features server
# 加载: src/config/staging.toml (需要创建该文件)
```

## 🔧 **环境变量覆盖**

可以使用环境变量覆盖配置文件中的设置：

```bash
# 覆盖数据库配置
APP__DATABASE__HOST=************* \
APP__DATABASE__PORT=3307 \
APP__DATABASE__USERNAME=myuser \
APP__DATABASE__PASSWORD=mypass \
cargo run --bin server --features server
```

环境变量格式：`APP__{SECTION}__{KEY}`
- 使用双下划线 `__` 作为分隔符
- 所有字母大写

## 📝 **配置加载优先级**

1. **默认配置** (`src/config/default.toml`)
2. **环境配置** (`src/config/{RUN_MODE}.toml`)
3. **环境变量** (`APP__*`)

后加载的配置会覆盖先加载的配置。

## 🔧 **数据库配置说明**

### **MySQL 配置**
```toml
[database]
db_type = "MySQL"
host = "localhost"
port = 3306
database = "ailurus"
username = "root"
password = "password"
max_connections = 10
```

### **PostgreSQL 配置**
```toml
[database]
db_type = "PostgreSQL"
host = "localhost"
port = 5432
database = "ailurus"
username = "postgres"
password = "password"
max_connections = 10
ssl_mode = "prefer"  # 可选
```

### **SQLite 配置**
```toml
[database]
db_type = "SQLite"
host = "localhost"    # 忽略
port = 0              # 忽略
database = "ailurus.db"
username = ""         # 忽略
password = ""         # 忽略
max_connections = 1
```

## 🔧 **添加新的配置项**

### **1. 修改 Settings 结构**
```rust
// src/model/settings.rs
#[derive(Debug, Clone, Deserialize)]
pub struct Settings {
    pub server: Server,
    pub database: DatabaseConfig,
    pub redis: RedisConfig,  // 新增
}

#[derive(Debug, Clone, Deserialize)]
pub struct RedisConfig {
    pub host: String,
    pub port: u16,
    pub password: Option<String>,
}
```

### **2. 更新配置文件**
```toml
# src/config/dev.toml
[server]
host = "127.0.0.1"
port = 3000

[database]
# ... 数据库配置

[redis]
host = "localhost"
port = 6379
password = ""
```

## 🔧 **故障排除**

### **配置文件未找到**
```
Loading configuration for environment: dev
Configuration loaded successfully
```
如果看到这个输出，说明配置加载成功。

### **配置文件格式错误**
```
Error: invalid type: string "invalid", expected a valid database type
```
检查 `db_type` 字段是否为 `"MySQL"`、`"PostgreSQL"` 或 `"SQLite"`。

### **数据库连接失败**
检查数据库配置是否正确：
- 主机地址和端口
- 用户名和密码
- 数据库名称
- 数据库服务是否运行

## 📝 **最佳实践**

1. **不要在代码中硬编码配置**
2. **使用环境变量存储敏感信息**
3. **为不同环境创建不同的配置文件**
4. **在 `.gitignore` 中排除包含敏感信息的配置文件**
5. **使用默认配置作为基础模板**

## 🔧 **示例：完整的启动流程**

```bash
# 1. 设置环境
export RUN_MODE=prod
export APP__DATABASE__PASSWORD=secure_password

# 2. 启动服务器
cargo run --bin server --features server

# 输出:
# Loading configuration for environment: prod
# Configuration loaded successfully
# HTTP 服务器运行在 0.0.0.0:3000
# gRPC 服务器运行在 0.0.0.0:9000
```

现在您的配置管理系统已经完全修复并且功能完善！
