[2m2025-07-02T08:16:11.379599Z[0m [32m INFO[0m [2mserver[0m[2m:[0m 这条日志会写入 logs/my_app.log    
[2m2025-07-02T08:16:11.485248Z[0m [32m INFO[0m [2msqlx::query[0m[2m:[0m [3msummary[0m[2m=[0m"SET sql_mode=(SELECT CONCAT(@@sql_mode, ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION')),time_zone='+00:00',NAMES …" [3mdb.statement[0m[2m=[0m"\n\nSET\n  sql_mode =(\n    SELECT\n      CONCAT(\n        @ @sql_mode,\n        ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION'\n      )\n  ),\n  time_zone = '+00:00',\n  NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;\n" [3mrows_affected[0m[2m=[0m0 [3mrows_returned[0m[2m=[0m0 [3melapsed[0m[2m=[0m1.777584ms [3melapsed_secs[0m[2m=[0m0.001777584
[2m2025-07-02T08:16:11.499837Z[0m [32m INFO[0m [2msqlx::query[0m[2m:[0m [3msummary[0m[2m=[0m"SET sql_mode=(SELECT CONCAT(@@sql_mode, ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION')),time_zone='+00:00',NAMES …" [3mdb.statement[0m[2m=[0m"\n\nSET\n  sql_mode =(\n    SELECT\n      CONCAT(\n        @ @sql_mode,\n        ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION'\n      )\n  ),\n  time_zone = '+00:00',\n  NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;\n" [3mrows_affected[0m[2m=[0m0 [3mrows_returned[0m[2m=[0m0 [3melapsed[0m[2m=[0m696.708µs [3melapsed_secs[0m[2m=[0m0.000696708
[2m2025-07-02T08:16:11.540736Z[0m [32m INFO[0m [2msqlx::query[0m[2m:[0m [3msummary[0m[2m=[0m"SET sql_mode=(SELECT CONCAT(@@sql_mode, ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION')),time_zone='+00:00',NAMES …" [3mdb.statement[0m[2m=[0m"\n\nSET\n  sql_mode =(\n    SELECT\n      CONCAT(\n        @ @sql_mode,\n        ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION'\n      )\n  ),\n  time_zone = '+00:00',\n  NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;\n" [3mrows_affected[0m[2m=[0m0 [3mrows_returned[0m[2m=[0m0 [3melapsed[0m[2m=[0m807.5µs [3melapsed_secs[0m[2m=[0m0.0008075
[2m2025-07-02T08:16:11.583539Z[0m [32m INFO[0m [2msqlx::query[0m[2m:[0m [3msummary[0m[2m=[0m"SET sql_mode=(SELECT CONCAT(@@sql_mode, ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION')),time_zone='+00:00',NAMES …" [3mdb.statement[0m[2m=[0m"\n\nSET\n  sql_mode =(\n    SELECT\n      CONCAT(\n        @ @sql_mode,\n        ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION'\n      )\n  ),\n  time_zone = '+00:00',\n  NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;\n" [3mrows_affected[0m[2m=[0m0 [3mrows_returned[0m[2m=[0m0 [3melapsed[0m[2m=[0m470.583µs [3melapsed_secs[0m[2m=[0m0.000470583
[2m2025-07-02T08:16:11.609150Z[0m [32m INFO[0m [2msqlx::query[0m[2m:[0m [3msummary[0m[2m=[0m"SET sql_mode=(SELECT CONCAT(@@sql_mode, ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION')),time_zone='+00:00',NAMES …" [3mdb.statement[0m[2m=[0m"\n\nSET\n  sql_mode =(\n    SELECT\n      CONCAT(\n        @ @sql_mode,\n        ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION'\n      )\n  ),\n  time_zone = '+00:00',\n  NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;\n" [3mrows_affected[0m[2m=[0m0 [3mrows_returned[0m[2m=[0m0 [3melapsed[0m[2m=[0m1.18825ms [3melapsed_secs[0m[2m=[0m0.00118825
[2m2025-07-02T08:16:27.413094Z[0m [32m INFO[0m [2msqlx::query[0m[2m:[0m [3msummary[0m[2m=[0m"SET sql_mode=(SELECT CONCAT(@@sql_mode, ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION')),time_zone='+00:00',NAMES …" [3mdb.statement[0m[2m=[0m"\n\nSET\n  sql_mode =(\n    SELECT\n      CONCAT(\n        @ @sql_mode,\n        ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION'\n      )\n  ),\n  time_zone = '+00:00',\n  NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;\n" [3mrows_affected[0m[2m=[0m0 [3mrows_returned[0m[2m=[0m0 [3melapsed[0m[2m=[0m2.1265ms [3melapsed_secs[0m[2m=[0m0.0021265
[2m2025-07-02T08:16:27.433850Z[0m [32m INFO[0m [2msqlx::query[0m[2m:[0m [3msummary[0m[2m=[0m"SET sql_mode=(SELECT CONCAT(@@sql_mode, ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION')),time_zone='+00:00',NAMES …" [3mdb.statement[0m[2m=[0m"\n\nSET\n  sql_mode =(\n    SELECT\n      CONCAT(\n        @ @sql_mode,\n        ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION'\n      )\n  ),\n  time_zone = '+00:00',\n  NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;\n" [3mrows_affected[0m[2m=[0m0 [3mrows_returned[0m[2m=[0m0 [3melapsed[0m[2m=[0m786.333µs [3melapsed_secs[0m[2m=[0m0.000786333
[2m2025-07-02T08:16:27.451741Z[0m [32m INFO[0m [2msqlx::query[0m[2m:[0m [3msummary[0m[2m=[0m"SET sql_mode=(SELECT CONCAT(@@sql_mode, ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION')),time_zone='+00:00',NAMES …" [3mdb.statement[0m[2m=[0m"\n\nSET\n  sql_mode =(\n    SELECT\n      CONCAT(\n        @ @sql_mode,\n        ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION'\n      )\n  ),\n  time_zone = '+00:00',\n  NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;\n" [3mrows_affected[0m[2m=[0m0 [3mrows_returned[0m[2m=[0m0 [3melapsed[0m[2m=[0m1.649291ms [3melapsed_secs[0m[2m=[0m0.001649291
[2m2025-07-02T08:16:27.464489Z[0m [32m INFO[0m [2msqlx::query[0m[2m:[0m [3msummary[0m[2m=[0m"SET sql_mode=(SELECT CONCAT(@@sql_mode, ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION')),time_zone='+00:00',NAMES …" [3mdb.statement[0m[2m=[0m"\n\nSET\n  sql_mode =(\n    SELECT\n      CONCAT(\n        @ @sql_mode,\n        ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION'\n      )\n  ),\n  time_zone = '+00:00',\n  NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;\n" [3mrows_affected[0m[2m=[0m0 [3mrows_returned[0m[2m=[0m0 [3melapsed[0m[2m=[0m686.709µs [3melapsed_secs[0m[2m=[0m0.000686709
[2m2025-07-02T08:16:27.475406Z[0m [32m INFO[0m [2msqlx::query[0m[2m:[0m [3msummary[0m[2m=[0m"SET sql_mode=(SELECT CONCAT(@@sql_mode, ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION')),time_zone='+00:00',NAMES …" [3mdb.statement[0m[2m=[0m"\n\nSET\n  sql_mode =(\n    SELECT\n      CONCAT(\n        @ @sql_mode,\n        ',PIPES_AS_CONCAT,NO_ENGINE_SUBSTITUTION'\n      )\n  ),\n  time_zone = '+00:00',\n  NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;\n" [3mrows_affected[0m[2m=[0m0 [3mrows_returned[0m[2m=[0m0 [3melapsed[0m[2m=[0m1.102ms [3melapsed_secs[0m[2m=[0m0.001102
