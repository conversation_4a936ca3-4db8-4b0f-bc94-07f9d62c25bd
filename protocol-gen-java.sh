#!/bin/bash

#gRPC Java Codegen Plugin for Protobuf Compiler
#==============================================
#
#This generates the Java interfaces out of the service definition from a
#`.proto` file. It works with the Protobuf Compiler (``protoc``).
#
#Normally you don't need to compile the codegen by yourself, since pre-compiled
#binaries for common platforms are available on Maven Central:
#
#1. Navigate to https://mvnrepository.com/artifact/io.grpc/protoc-gen-grpc-java
#2. Click into a version
#3. Click "Files"

# https://github.com/grpc/grpc-java/tree/master/compiler
# 查看compiler/src/java_plugin/cpp/java_generator.cpp文件
# 查看compiler/build.gradle文件

## To generate Java interfaces with protobuf lite:
#protoc --plugin=protoc-gen-grpc-java=/usr/local/bin/protoc-gen-grpc-java-1.66.0-osx-aarch_64.exe \
#  --grpc-java_out=lite:"." --proto_path=. ./src/main/proto/Global.proto

# 加载 utils.sh 脚本
source ./utils.sh

########################################################################################################################

work_dir=$(realpath "$(dirname "$(dirname "$0")")")
module_dir=${1:-}
out_dir="$work_dir/${module_dir:+$module_dir/}src/main/gen"
mkdir -p "$out_dir"

echo "Change work directory..." && cd "$work_dir" || exit 1
echo "work_dir: $work_dir"
echo "module_dir: $module_dir"
echo "out_dir: $out_dir"

########################################################################################################################
# thrift
########################################################################################################################
echo "Generating thrift stub code..." && thrift --version
check_thrift_version "0.20.0"
THRIFT_DIR="protocol/thrift"
# 遍历目录下的所有thrift文件，并编译成Java类
find ${THRIFT_DIR} -name "*.thrift" | while read file; do
  echo -e "\nFind thrift file: ${file}"
  thrift -strict -recurse --gen java:jakarta_annotations,generated_annotations=suppress -out $out_dir ${file}
done

########################################################################################################################
# protoc
########################################################################################################################
echo "Generating protoc stub code..." && protoc --version
# To compile a proto file and generate Java interfaces out of the service definitions:
protoc --plugin=protoc-gen-grpc-java=/usr/local/bin/protoc-gen-grpc-java-1.66.0-osx-aarch_64.exe \
--java_out="$out_dir" \
--grpc-java_out="$out_dir" --grpc-java_opt="@generated=omit" \
protocol/grpc/**/*.proto