syntax = "proto3";

package totoro_infrastructure;

option java_multiple_files = true;
option java_package = "org.ponderers.totoro.infrastructure.rpc.grpc.hello";
option java_outer_classname = "HelloWorldProto";
option go_package = "org.ponderers.totoro.infrastructure.rpc.grpc.hello";

import "google/protobuf/empty.proto";

// The greeting service definition.
service Simple {
  // Sends a greeting
  rpc SayHello (HelloRequest) returns (HelloReply) {
  }
  rpc SayHelloAgain (HelloRequest) returns (HelloReply) {}

  rpc UnaryRpc (UnaryRequest) returns (UnaryResponse) {}
  rpc ClientStreamingRpc (stream ClientStreamingRequest) returns (ClientStreamingResponse) {}
  rpc ServerStreamingRpc (ServerStreamingRequest) returns (stream ServerStreamingResponse) {}
  rpc BidiStreamingRpc (stream BidiStreamingRequest) returns (stream BidiStreamingResponse) {}
  rpc CloseLogTailer (google.protobuf.Empty) returns (CloseLogTailerResponse) {}
}

message CloseLogTailerResponse {
  string result = 1;
}

// The request message containing the user's name.
message HelloRequest {
  string name = 1;
}

// The response message containing the greetings
message HelloReply {
  string message = 1;
}

message UnaryRequest {
  string message = 1;
}

message UnaryResponse {
  string message = 1;
}

message ClientStreamingRequest {
  string message = 1;
}

message ClientStreamingResponse {
  string message = 1;
}

message ServerStreamingRequest {
  string message = 1;
}

message ServerStreamingResponse {
  string message = 1;
}

message BidiStreamingRequest {
  string message = 1;
}

message BidiStreamingResponse {
  string message = 1;
}

