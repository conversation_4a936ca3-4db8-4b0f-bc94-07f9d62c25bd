syntax = "proto3";

package totoro_infrastructure;

option java_multiple_files = true;
option java_package = "org.ponderers.totoro.infrastructure.rpc.grpc.unique";
option go_package = "org.ponderers.totoro.infrastructure.rpc.grpc.unique";

import "google/protobuf/empty.proto";

service UniqueIdService {
  rpc Get (google.protobuf.Empty) returns (GetResponse);
  rpc Gets (GetsRequest) returns (GetsResponse);
}

message GetResponse {
  int64 uniqueId = 1;
}

message GetsRequest {
  int32 num = 1;
}

message GetsResponse {
  repeated int64 uniqueId = 1;
}

