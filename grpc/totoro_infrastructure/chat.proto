syntax = "proto3";

package totoro_infrastructure;

option java_multiple_files = true;
option java_package = "org.ponderers.totoro.infrastructure.rpc.grpc.chat";

service ChatService {
  rpc JoinRoom (stream JoinRequest) returns (stream ChatMessage);
  rpc SendMessage (ChatMessage) returns (Empty);
}

message JoinRequest {
  string username = 1;
}

message ChatMessage {
  string username = 1;
  string message = 2;
}

message Empty {}