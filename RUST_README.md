# UniqueId Service - Thrift Implementation in Rust

This project implements a UniqueId service using Apache Thrift with Rust. The service generates unique IDs using the Snowflake algorithm.

## Features

- **Snowflake Algorithm**: Generates 64-bit unique IDs with timestamp, worker ID, and sequence components
- **High Performance**: Can generate thousands of IDs per second
- **Thread Safe**: Uses atomic operations for concurrent access
- **Thrift Protocol**: Standard binary protocol for cross-language compatibility
- **Error Handling**: Comprehensive error handling and validation
- **Logging**: Structured logging for monitoring and debugging

## Quick Start

1. **Generate Thrift code:**
   ```bash
   ./protocol-gen-rust.sh
   ```

2. **Build and run server:**
   ```bash
   cargo run --bin server
   ```

3. **Test with client (in another terminal):**
   ```bash
   cargo run --bin client
   ```

## API

- `get()` - Generate single unique ID
- `gets(num)` - Generate multiple unique IDs (1-1000)

## Usage Examples

```bash
# Start server with worker ID 1 on port 9090
cargo run --bin server 1 127.0.0.1:9090

# Run client tests
cargo run --bin client 127.0.0.1:9090
```

See full documentation in the source code and comments.
