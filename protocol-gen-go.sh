#!/bin/bash

# 加载 utils.sh 脚本
source ./utils.sh

########################################################################################################################

work_dir=$(realpath "$(dirname "$(dirname "$0")")")
module_dir=${1:-}
generated_dir="$work_dir/$module_dir/pkg/gen"
mkdir -p "$generated_dir"

echo "Change work directory..." && cd "$work_dir" || exit 1
echo "work_dir: $work_dir"
echo "module_dir: $module_dir"
echo "generated_dir: $generated_dir"

########################################################################################################################
# thrift
########################################################################################################################
echo "Generating thrift stub code..." && thrift --version
THRIFT_DIR="protocol/thrift"
# 遍历目录下的所有thrift文件，并编译成Java类
find ${THRIFT_DIR} -name "*.thrift" | while read file; do
  echo "Find thrift file: ${file}"
  thrift -strict -verbose -recurse --gen go -out $generated_dir ${file}
done
