# ailurus

## 📁 **配置文件结构**

```
src/config/
├── default.toml    # 默认配置
├── dev.toml        # 开发环境配置
├── prod.toml       # 生产环境配置
├── test.toml       # 测试环境配置
└── mod.rs          # 模块声明
```
## 🔧 **配置文件格式**

所有配置文件都使用统一的 TOML 格式：

```toml
[server]
host = "127.0.0.1"
port = 3000

[database]
db_type = "MySQL"           # MySQL, PostgreSQL, SQLite
host = "localhost"
port = 3306
database = "ailurus"
username = "root"
password = ""
max_connections = 10
```

## 🚀 **使用方法**

### **3. 测试环境**
```bash
RUN_MODE=test cargo run --bin server --features server
# 加载: src/config/test.toml
```

## 🔧 **环境变量覆盖**

可以使用环境变量覆盖配置文件中的设置：

```bash
# 覆盖数据库配置
APP__DATABASE__HOST=************* \
APP__DATABASE__PORT=3307 \
APP__DATABASE__USERNAME=myuser \
APP__DATABASE__PASSWORD=mypass \
cargo run --bin server --features server
```

环境变量格式：`APP__{SECTION}__{KEY}`
- 使用双下划线 `__` 作为分隔符
- 所有字母大写

## 📝 **配置加载优先级**

1. **默认配置** (`src/config/default.toml`)
2. **环境配置** (`src/config/{RUN_MODE}.toml`)
3. **环境变量** (`APP__*`)

后加载的配置会覆盖先加载的配置。