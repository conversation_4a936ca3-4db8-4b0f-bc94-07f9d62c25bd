[package]
name = "rust_demo"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
chrono = "0.4.33"
mysql = "20.0.0"
diesel = { version = "2.1.0", features = ["postgres"] }
dotenvy = "0.15"
sea-orm = { version = "^0.12.0", features = [ "sqlx-mysql", "runtime-async-std-native-tls", "macros" ] }
futures = "0.3.31"
rand = "0.8.5"
thiserror = "1.0"
axum = { version = "0.7.4", features = ["http2"] }

tonic = "0.8.3"
tonic-build = "0.8.4"
prost = "0.11.3"
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tower = "0.4.13"
tower-http = { version = "0.5.2", features = ["cors"] }
hyper = { version = "1.0", features = ["full"] }
hyper-util = { version = "0.1", features = ["tokio"] }
http = "0.2.12"
dotenv = "0.15.0"
random-nickname2 = "0.1.0"
thrift = "0.17.0"



[build-dependencies]
tonic-build = "0.8.4"

[features]
server = []
gen_proto = []

[[bin]]
name = "server"
path = "src/bin/server.rs"
required-features = ["server"]

[[bin]]
name = "gen_proto"
path = "src/bin/gen_proto.rs"
required-features = ["gen_proto"]
