[package]
name = "unique_id_service"
version = "0.1.0"
edition = "2021"

[dependencies]
thrift = "0.17.0"
tokio = { version = "1.0", features = ["full"] }
rand = "0.8.5"
chrono = { version = "0.4", features = ["serde"] }
log = "0.4"
env_logger = "0.10"

[lib]
name = "unique_id_service"
path = "src/lib.rs"

[[bin]]
name = "server"
path = "src/bin/server.rs"

[[bin]]
name = "client"
path = "src/bin/client.rs"
