#!/bin/bash
########################################################################################################################
# 加载 utils.sh 脚本，用法：source ./utils.sh
########################################################################################################################
# 严格模式
set -euo pipefail # 兼容性写法（部分 shell 可能不支持 pipefail）
shopt -s failglob nullglob

# 调试模式（按需启用），用法：DEBUG=true ./your_script.sh
if [[ "${DEBUG:-}" == "true" ]]; then
  set -xv
fi

########################################################################################################################

check_thrift_version() {
    echo "检查Thrift版本..."
    local expect_version="$1"
    local actual_version=$(thrift --version 2>&1 | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
    if [[ "$actual_version" == "$expect_version" ]]; then
        echo "Thrift 版本正确: $actual_version"
        return 0
    else
        echo "Thrift 版本不符，当前: ${actual_version:-}，期望: $expect_version"
        return 1
    fi
}
